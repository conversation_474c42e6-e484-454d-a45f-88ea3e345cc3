package com.jcbj.mall.set.controller;

import com.jcbj.mall.common.core.util.R;
import com.jcbj.mall.set.entity.PubSysResource;
import com.jcbj.mall.set.entity.PubSysRole;
import com.jcbj.mall.set.entity.dto.PubSysRoleDTO;
import com.jcbj.mall.set.entity.vo.PubSysResourceVO;
import com.jcbj.mall.set.entity.vo.PubSysRoleVO;
import com.jcbj.mall.set.service.PubSysResourceService;
import com.jcbj.mall.set.service.PubSysRoleService;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/set/role")
@RequiredArgsConstructor
@Tag(name = "角色管理")
public class PubSysRoleController {

    private final PubSysRoleService pubSysRoleService;
    private final PubSysResourceService pubSysResourceService;

    /**
     * 分页查询角色列表
     *
     * @param current 当前页
     * @param size    每页记录数
     * @param role    角色名称（可选）
     * @return 角色分页数据
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询角色列表")
    public R<Page<PubSysRoleVO>> page(PubSysRoleDTO roleDTO) {
        return R.ok(pubSysRoleService.getRolePage(roleDTO), "获取角色分页数据成功");
    }

    /**
     * 获取角色详情
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取角色详情")
    public R<PubSysRoleVO> getById(@PathVariable Long id) {
        return R.ok(pubSysRoleService.getRoleById(id), "获取角色详情成功");
    }

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 创建的角色信息
     */
    @PostMapping
    @Operation(summary = "创建角色")
    public R<PubSysRoleVO> create(@RequestBody PubSysRoleDTO roleDTO) {
        try {
            Boolean success = pubSysRoleService.createRole(roleDTO);
            if (success) {
                // 返回创建后的角色信息
                PubSysRoleVO createdRole = pubSysRoleService.getRoleByName(roleDTO.getRole());
                return R.ok(createdRole, "创建角色成功");
            }
            return R.failed("创建角色失败");
        } catch (RuntimeException e) {
            // 处理角色名称重复等业务异常
            return R.failed(e.getMessage());
        }
    }

    /**
     * 更新角色
     *
     * @param id      角色ID
     * @param roleDTO 角色信息
     * @return 更新后的角色信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新角色")
    public R<PubSysRoleVO> update(@PathVariable Long id, @RequestBody PubSysRoleDTO roleDTO) {
        try {
            roleDTO.setId(id);
            Boolean success = pubSysRoleService.updateRole(roleDTO);
            if (success) {
                // 返回更新后的角色信息
                PubSysRoleVO updatedRole = pubSysRoleService.getRoleById(id);
                return R.ok(updatedRole, "更新角色成功");
            }
            return R.failed("更新角色失败");
        } catch (RuntimeException e) {
            // 处理角色名称重复等业务异常
            return R.failed(e.getMessage());
        }
    }

    // /**
    //  * 删除角色
    //  *
    //  * @param id 角色ID
    //  * @return 删除结果
    //  */
    // @DeleteMapping("/{id}")
    // @Operation(summary = "删除角色")
    // public R<Boolean> delete(@PathVariable Long id) {
    //     return R.ok(pubSysRoleService.deleteRole(id), "删除角色成功");
    // }

    // /**
    //  * 获取所有角色
    //  *
    //  * @return 角色列表
    //  */
    // @GetMapping("/list")
    // @Operation(summary = "获取所有角色")
    // public R<List<PubSysRoleVO>> list() {
    //     return R.ok(pubSysRoleService.getAllRoles(), "获取所有角色成功");
    // }

    /**
     * 获取角色权限列表
     *
     * @param roleId 角色ID
     * @return 角色拥有的权限ID列表
     */
    @GetMapping("/{roleId}/resources")
    @Operation(summary = "获取角色权限列表")
    public R<Map<String, Object>> getRoleResources(@PathVariable Long roleId) {
        List<String> resourceIds = pubSysRoleService.getRoleResources(roleId);
        PubSysRoleVO roleInfo = pubSysRoleService.getRoleById(roleId);

        Map<String, Object> result = new HashMap<>();
        result.put("roleId", roleId);
        result.put("roleName", roleInfo != null ? roleInfo.getRole() : "");
        result.put("resourceIds", resourceIds);
        result.put("resourceCount", resourceIds.size());

        return R.ok(result, "获取角色权限列表成功");
    }

    /**
     * 获取资源树
     *
     * @return 资源树
     */
    @GetMapping("/resources/tree")
    @Operation(summary = "获取资源树")
    public R<List<PubSysResourceVO>> getResourceTree() {
        return R.ok(pubSysResourceService.buildTree(), "获取资源树成功");
    }

    /**
     * 获取带选中状态的资源树
     *
     * @param roleId 角色ID（可选，如果提供则标记该角色已选中的资源）
     * @return 资源树
     */
    @GetMapping("/resources/tree/selected")
    @Operation(summary = "获取带选中状态的资源树")
    public R<Map<String, Object>> getResourceTreeWithSelected(@RequestParam(required = false) Long roleId) {
        List<PubSysResourceVO> tree;
        Map<String, Object> result = new HashMap<>();

        if (roleId != null) {
            // 获取角色已选中的资源ID
            List<String> selectedResourceIds = pubSysRoleService.getRoleResources(roleId);
            tree = pubSysResourceService.buildTreeWithSelected(selectedResourceIds);

            PubSysRoleVO roleInfo = pubSysRoleService.getRoleById(roleId);
            result.put("roleId", roleId);
            result.put("roleName", roleInfo != null ? roleInfo.getRole() : "");
            result.put("selectedCount", selectedResourceIds.size());
        } else {
            tree = pubSysResourceService.buildTree();
            result.put("selectedCount", 0);
        }

        result.put("tree", tree);
        result.put("totalCount", countTotalResources(tree));

        return R.ok(result, "获取资源树成功");
    }

    /**
     * 递归计算资源总数
     */
    private int countTotalResources(List<PubSysResourceVO> tree) {
        int count = 0;
        for (PubSysResourceVO resource : tree) {
            count++;
            if (resource.getChildren() != null && !resource.getChildren().isEmpty()) {
                count += countTotalResources(resource.getChildren());
            }
        }
        return count;
    }

    /**
     * 设置角色权限
     *
     * @param roleId 角色ID
     * @param requestBody 权限设置请求体
     * @return 设置结果和更新后的权限信息
     */
    @PostMapping("/{roleId}/resources")
    @Operation(summary = "设置角色权限")
    public R<Map<String, Object>> setResources(@PathVariable Long roleId, @RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<Object> resourceIdsObj = (List<Object>) requestBody.get("resourceIds");

            // 将Object列表转换为String列表，保持原始格式
            List<String> resourceIds = null;
            if (resourceIdsObj != null) {
                resourceIds = resourceIdsObj.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }

            Boolean success = pubSysRoleService.assignResources(roleId, resourceIds);

            if (success) {
                // 返回设置后的权限信息
                List<String> updatedResourceIds = pubSysRoleService.getRoleResources(roleId);
                PubSysRoleVO roleInfo = pubSysRoleService.getRoleById(roleId);

                Map<String, Object> result = new HashMap<>();
                result.put("roleId", roleId);
                result.put("roleName", roleInfo != null ? roleInfo.getRole() : "");
                result.put("resourceIds", updatedResourceIds);
                result.put("resourceCount", updatedResourceIds.size());
                result.put("assignedCount", resourceIds != null ? resourceIds.size() : 0);

                return R.ok(result, "设置角色权限成功");
            }

            return R.failed("设置角色权限失败");
        } catch (RuntimeException e) {
            return R.failed(e.getMessage());
        }
    }

    /**
     * 切换角色状态
     *
     * @param id     角色ID
     * @param status 状态：0-禁用，1-启用
     * @return 更新结果
     */
    @PutMapping("/{id}/status/{status}")
    @Operation(summary = "切换角色状态")
    public R<PubSysRoleVO> toggleStatus(@PathVariable Long id, @PathVariable Integer status) {
        try {
            PubSysRoleVO result = pubSysRoleService.toggleStatus(id, status);
            return R.ok(result, "切换角色状态成功");
        } catch (RuntimeException e) {
            return R.failed(e.getMessage());
        }
    }
}
