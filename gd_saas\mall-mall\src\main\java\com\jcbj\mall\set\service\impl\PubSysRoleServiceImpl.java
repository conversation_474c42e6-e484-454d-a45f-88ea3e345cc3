package com.jcbj.mall.set.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jcbj.mall.set.entity.PubSysRole;
import com.jcbj.mall.set.entity.dto.PubSysRoleDTO;
import com.jcbj.mall.set.entity.vo.PubSysRoleVO;
import com.jcbj.mall.set.mapper.PubSysRoleMapper;
import com.jcbj.mall.set.service.PubSysRoleService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.jcbj.mall.set.entity.table.PubSysRoleTableDef.PUB_SYS_ROLE;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PubSysRoleServiceImpl extends ServiceImpl<PubSysRoleMapper, PubSysRole> implements PubSysRoleService {

    /**
     * 分页查询角色列表
     *
     * @param roleDTO 查询条件
     * @return 角色分页数据
     */
    @Override
    public Page<PubSysRoleVO> getRolePage(PubSysRoleDTO roleDTO) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(PUB_SYS_ROLE.ALL_COLUMNS)
                .from(PUB_SYS_ROLE);

        // 根据角色名称查询
        if (StringUtils.hasText(roleDTO.getRole())) {
            queryWrapper.and(PUB_SYS_ROLE.ROLE.like("%" + roleDTO.getRole() + "%"));
        }

        // 根据状态查询
        if (roleDTO.getAvailable() != null) {
            queryWrapper.and(PUB_SYS_ROLE.AVAILABLE.eq(roleDTO.getAvailable()));
        }

        queryWrapper.orderBy(PUB_SYS_ROLE.ID.asc());

        // 创建分页对象
        Page<PubSysRole> page = new Page<>(
                roleDTO.getCurrent() != null ? roleDTO.getCurrent() : 1,
                roleDTO.getSize() != null ? roleDTO.getSize() : 10);

        // 执行分页查询
        Page<PubSysRole> entityPage = this.page(page, queryWrapper);

        // 转换为VO分页对象
        Page<PubSysRoleVO> voPage = new Page<>();
        voPage.setPageNumber(entityPage.getPageNumber());
        voPage.setPageSize(entityPage.getPageSize());
        voPage.setTotalRow(entityPage.getTotalRow());
        voPage.setRecords(entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList()));

        return voPage;
    }

    /**
     * 获取角色详情
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @Override
    public PubSysRoleVO getRoleById(Long id) {
        PubSysRole role = super.getById(id);
        return role != null ? convertToVO(role) : null;
    }

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createRole(PubSysRoleDTO roleDTO) {
        log.info("创建角色: {}", roleDTO.getRole());

        // 基本参数验证
        if (roleDTO == null) {
            throw new RuntimeException("角色信息不能为空");
        }
        if (roleDTO.getRole() == null || roleDTO.getRole().trim().isEmpty()) {
            throw new RuntimeException("角色名称不能为空");
        }

        // 检查角色名称是否已存在
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(PUB_SYS_ROLE.ID)
                .from(PUB_SYS_ROLE)
                .where(PUB_SYS_ROLE.ROLE.eq(roleDTO.getRole().trim()));

        PubSysRole existingRole = getOne(queryWrapper);
        if (existingRole != null) {
            log.warn("角色名称已存在: {}", roleDTO.getRole());
            throw new RuntimeException("角色名称已存在");
        }

        PubSysRole role = convertToEntity(roleDTO);
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        // 如果没有设置状态，默认为启用
        if (role.getAvailable() == null) {
            role.setAvailable(1);
        }

        boolean result = save(role);
        log.info("创建角色结果: {}, 角色名称: {}", result, roleDTO.getRole());
        return result;
    }

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRole(PubSysRoleDTO roleDTO) {
        log.info("更新角色: ID={}, 角色名称={}", roleDTO.getId(), roleDTO.getRole());

        // 基本参数验证
        if (roleDTO == null) {
            throw new RuntimeException("角色信息不能为空");
        }
        if (roleDTO.getId() == null) {
            throw new RuntimeException("角色ID不能为空");
        }
        if (roleDTO.getRole() == null || roleDTO.getRole().trim().isEmpty()) {
            throw new RuntimeException("角色名称不能为空");
        }

        // 检查角色是否存在
        PubSysRole existingRole = super.getById(roleDTO.getId());
        if (existingRole == null) {
            log.warn("角色不存在，ID: {}", roleDTO.getId());
            throw new RuntimeException("角色不存在");
        }

        // 检查角色名称是否已被其他角色使用
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(PUB_SYS_ROLE.ID)
                .from(PUB_SYS_ROLE)
                .where(PUB_SYS_ROLE.ROLE.eq(roleDTO.getRole().trim()))
                .and(PUB_SYS_ROLE.ID.ne(roleDTO.getId()));

        PubSysRole duplicateRole = getOne(queryWrapper);
        if (duplicateRole != null) {
            log.warn("角色名称已被其他角色使用: {}", roleDTO.getRole());
            throw new RuntimeException("角色名称已存在");
        }

        PubSysRole role = convertToEntity(roleDTO);
        // 保留原有的创建时间
        role.setCreateTime(existingRole.getCreateTime());
        role.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(role);
        log.info("更新角色结果: {}, 角色ID: {}", result, roleDTO.getId());
        return result;
    }

    // /**
    //  * 删除角色
    //  *
    //  * @param id 角色ID
    //  * @return 是否成功
    //  */
    // @Override
    // @Transactional(rollbackFor = Exception.class)
    // public Boolean deleteRole(Long id) {
    //     return removeById(id);
    // }

    // /**
    //  * 获取所有角色
    //  *
    //  * @return 角色列表
    //  */
    // @Override
    // public List<PubSysRoleVO> getAllRoles() {
    //     List<PubSysRole> roles = list();
    //     return roles.stream()
    //             .map(this::convertToVO)
    //             .collect(Collectors.toList());
    // }

    /**
     * 为角色分配资源
     *
     * @param roleId      角色ID
     * @param resourceIds 资源ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignResources(Long roleId, List<String> resourceIds) {
        log.info("为角色{}分配资源: {}", roleId, resourceIds);

        if (roleId == null) {
            log.warn("角色ID不能为空");
            throw new RuntimeException("角色ID不能为空");
        }

        // 检查角色是否存在
        PubSysRole role = super.getById(roleId);
        if (role == null) {
            log.warn("角色不存在，ID: {}", roleId);
            throw new RuntimeException("角色不存在");
        }

        // 将资源ID列表转换为逗号分隔的字符串（保持原始格式）
        String resourceIdsStr = "";
        if (resourceIds != null && !resourceIds.isEmpty()) {
            resourceIdsStr = String.join(",", resourceIds);
        }

        // 更新角色的资源权限
        role.setResourceIds(resourceIdsStr);
        role.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(role);
        log.info("角色{}资源分配结果: {}, 分配的资源: {}", roleId, result, resourceIdsStr);
        return result;
    }

    /**
     * 获取角色的资源列表
     *
     * @param roleId 角色ID
     * @return 资源ID列表
     */
    @Override
    public List<String> getRoleResources(Long roleId) {
        log.info("获取角色{}的资源列表", roleId);

        if (roleId == null) {
            log.warn("角色ID不能为空");
            return new ArrayList<>();
        }

        // 获取角色信息
        PubSysRole role = super.getById(roleId);
        if (role == null) {
            log.warn("角色不存在，ID: {}", roleId);
            return new ArrayList<>();
        }

        // 解析资源ID字符串
        String resourceIds = role.getResourceIds();
        if (resourceIds == null || resourceIds.trim().isEmpty()) {
            log.info("角色{}没有分配任何资源", roleId);
            return new ArrayList<>();
        }

        // 将逗号分隔的字符串转换为字符串列表（保持原始格式，如"0105"）
        List<String> resourceIdList = Arrays.stream(resourceIds.split(","))
                .filter(id -> id != null && !id.trim().isEmpty())
                .map(String::trim)
                .collect(Collectors.toList());

        log.info("角色{}的资源列表: {}", roleId, resourceIdList);
        return resourceIdList;
    }

    /**
     * 根据角色名称查询角色
     *
     * @param roleName 角色名称
     * @return 角色信息
     */
    @Override
    public PubSysRoleVO getRoleByName(String roleName) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(PUB_SYS_ROLE.ALL_COLUMNS)
                .from(PUB_SYS_ROLE)
                .where(PUB_SYS_ROLE.ROLE.eq(roleName));

        PubSysRole role = getOne(queryWrapper);
        return role != null ? convertToVO(role) : null;
    }

    /**
     * 实体转VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private PubSysRoleVO convertToVO(PubSysRole entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, PubSysRoleVO.class);
    }

    /**
     * DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    private PubSysRole convertToEntity(PubSysRoleDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, PubSysRole.class);
    }

    /**
     * 切换角色状态
     *
     * @param id     角色ID
     * @param status 状态值
     * @return 更新后的角色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PubSysRoleVO toggleStatus(Long id, Integer status) {
        log.info("切换角色状态，角色ID: {}, 状态: {}", id, status);

        if (id == null) {
            log.warn("角色ID不能为空");
            throw new RuntimeException("角色ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            log.warn("状态值无效: {}", status);
            throw new RuntimeException("状态值必须为0（禁用）或1（启用）");
        }

        PubSysRole role = super.getById(id);
        if (role == null) {
            log.warn("角色不存在，ID: {}", id);
            throw new RuntimeException("角色不存在");
        }

        role.setAvailable(status);
        role.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(role);
        log.info("角色状态切换结果: {}", result);

        if (result) {
            return convertToVO(getById(id));
        }
        throw new RuntimeException("状态切换失败");
    }
}
